
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeNinjaFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Program used to build from build.ninja files."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ninja-build"
      - "ninja"
      - "samu"
    candidate_directories:
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/MinGW/bin/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
    searched_directories:
      - "C:/Python312/Scripts/ninja-build.com"
      - "C:/Python312/Scripts/ninja-build.exe"
      - "C:/Python312/Scripts/ninja-build"
      - "C:/Python312/Scripts/ninja.com"
      - "C:/Python312/Scripts/ninja.exe"
      - "C:/Python312/Scripts/ninja"
      - "C:/Python312/Scripts/samu.com"
      - "C:/Python312/Scripts/samu.exe"
      - "C:/Python312/Scripts/samu"
      - "C:/Python312/ninja-build.com"
      - "C:/Python312/ninja-build.exe"
      - "C:/Python312/ninja-build"
      - "C:/Python312/ninja.com"
      - "C:/Python312/ninja.exe"
      - "C:/Python312/ninja"
      - "C:/Python312/samu.com"
      - "C:/Python312/samu.exe"
      - "C:/Python312/samu"
      - "C:/Program Files/Oculus/Support/oculus-runtime/ninja-build.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/ninja-build.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/ninja-build"
      - "C:/Program Files/Oculus/Support/oculus-runtime/ninja.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/ninja.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/ninja"
      - "C:/Program Files/Oculus/Support/oculus-runtime/samu.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/samu.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/samu"
      - "C:/Windows/System32/ninja-build.com"
      - "C:/Windows/System32/ninja-build.exe"
      - "C:/Windows/System32/ninja-build"
      - "C:/Windows/System32/ninja.com"
      - "C:/Windows/System32/ninja.exe"
      - "C:/Windows/System32/ninja"
      - "C:/Windows/System32/samu.com"
      - "C:/Windows/System32/samu.exe"
      - "C:/Windows/System32/samu"
      - "C:/Windows/ninja-build.com"
      - "C:/Windows/ninja-build.exe"
      - "C:/Windows/ninja-build"
      - "C:/Windows/ninja.com"
      - "C:/Windows/ninja.exe"
      - "C:/Windows/ninja"
      - "C:/Windows/samu.com"
      - "C:/Windows/samu.exe"
      - "C:/Windows/samu"
      - "C:/Windows/System32/wbem/ninja-build.com"
      - "C:/Windows/System32/wbem/ninja-build.exe"
      - "C:/Windows/System32/wbem/ninja-build"
      - "C:/Windows/System32/wbem/ninja.com"
      - "C:/Windows/System32/wbem/ninja.exe"
      - "C:/Windows/System32/wbem/ninja"
      - "C:/Windows/System32/wbem/samu.com"
      - "C:/Windows/System32/wbem/samu.exe"
      - "C:/Windows/System32/wbem/samu"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja-build.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja-build.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja-build"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/samu.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/samu.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/samu"
      - "C:/Windows/System32/OpenSSH/ninja-build.com"
      - "C:/Windows/System32/OpenSSH/ninja-build.exe"
      - "C:/Windows/System32/OpenSSH/ninja-build"
      - "C:/Windows/System32/OpenSSH/ninja.com"
      - "C:/Windows/System32/OpenSSH/ninja.exe"
      - "C:/Windows/System32/OpenSSH/ninja"
      - "C:/Windows/System32/OpenSSH/samu.com"
      - "C:/Windows/System32/OpenSSH/samu.exe"
      - "C:/Windows/System32/OpenSSH/samu"
      - "C:/Program Files/Git/cmd/ninja-build.com"
      - "C:/Program Files/Git/cmd/ninja-build.exe"
      - "C:/Program Files/Git/cmd/ninja-build"
      - "C:/Program Files/Git/cmd/ninja.com"
      - "C:/Program Files/Git/cmd/ninja.exe"
      - "C:/Program Files/Git/cmd/ninja"
      - "C:/Program Files/Git/cmd/samu.com"
      - "C:/Program Files/Git/cmd/samu.exe"
      - "C:/Program Files/Git/cmd/samu"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja-build.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja-build.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja-build"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/samu.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/samu.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/samu"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja-build.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja-build.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja-build"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/samu.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/samu.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/samu"
      - "C:/Program Files/dotnet/ninja-build.com"
      - "C:/Program Files/dotnet/ninja-build.exe"
      - "C:/Program Files/dotnet/ninja-build"
      - "C:/Program Files/dotnet/ninja.com"
      - "C:/Program Files/dotnet/ninja.exe"
      - "C:/Program Files/dotnet/ninja"
      - "C:/Program Files/dotnet/samu.com"
      - "C:/Program Files/dotnet/samu.exe"
      - "C:/Program Files/dotnet/samu"
      - "C:/MinGW/bin/ninja-build.com"
      - "C:/MinGW/bin/ninja-build.exe"
      - "C:/MinGW/bin/ninja-build"
      - "C:/MinGW/bin/ninja.com"
      - "C:/MinGW/bin/ninja.exe"
      - "C:/MinGW/bin/ninja"
      - "C:/MinGW/bin/samu.com"
      - "C:/MinGW/bin/samu.exe"
      - "C:/MinGW/bin/samu"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/ninja-build.com"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/ninja-build.exe"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/ninja-build"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/ninja.com"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/ninja.exe"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/ninja"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/samu.com"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/samu.exe"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/samu"
      - "C:/Program Files/nodejs/ninja-build.com"
      - "C:/Program Files/nodejs/ninja-build.exe"
      - "C:/Program Files/nodejs/ninja-build"
      - "C:/Program Files/nodejs/ninja.com"
      - "C:/Program Files/nodejs/ninja.exe"
      - "C:/Program Files/nodejs/ninja"
      - "C:/Program Files/nodejs/samu.com"
      - "C:/Program Files/nodejs/samu.exe"
      - "C:/Program Files/nodejs/samu"
      - "C:/ProgramData/chocolatey/bin/ninja-build.com"
      - "C:/ProgramData/chocolatey/bin/ninja-build.exe"
      - "C:/ProgramData/chocolatey/bin/ninja-build"
      - "C:/ProgramData/chocolatey/bin/ninja.com"
      - "C:/ProgramData/chocolatey/bin/ninja.exe"
      - "C:/ProgramData/chocolatey/bin/ninja"
      - "C:/ProgramData/chocolatey/bin/samu.com"
      - "C:/ProgramData/chocolatey/bin/samu.exe"
      - "C:/ProgramData/chocolatey/bin/samu"
      - "C:/Program Files/Docker/Docker/resources/bin/ninja-build.com"
      - "C:/Program Files/Docker/Docker/resources/bin/ninja-build.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/ninja-build"
      - "C:/Program Files/Docker/Docker/resources/bin/ninja.com"
      - "C:/Program Files/Docker/Docker/resources/bin/ninja.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/ninja"
      - "C:/Program Files/Docker/Docker/resources/bin/samu.com"
      - "C:/Program Files/Docker/Docker/resources/bin/samu.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/samu"
      - "C:/Program Files/Graphviz/bin/ninja-build.com"
      - "C:/Program Files/Graphviz/bin/ninja-build.exe"
      - "C:/Program Files/Graphviz/bin/ninja-build"
      - "C:/Program Files/Graphviz/bin/ninja.com"
      - "C:/Program Files/Graphviz/bin/ninja.exe"
      - "C:/Program Files/Graphviz/bin/ninja"
      - "C:/Program Files/Graphviz/bin/samu.com"
      - "C:/Program Files/Graphviz/bin/samu.exe"
      - "C:/Program Files/Graphviz/bin/samu"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/ninja-build.com"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/ninja-build.exe"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/ninja-build"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/ninja.com"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/ninja.exe"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/ninja"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/samu.com"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/samu.exe"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/samu"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/ninja-build.com"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/ninja-build.exe"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/ninja-build"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/ninja.com"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/ninja.exe"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/ninja"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/samu.com"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/samu.exe"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/samu"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/ninja-build.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/ninja-build.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/ninja-build"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/ninja.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/ninja.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/ninja"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/samu.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/samu.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/samu"
      - "C:/Program Files/CMake/bin/ninja-build.com"
      - "C:/Program Files/CMake/bin/ninja-build.exe"
      - "C:/Program Files/CMake/bin/ninja-build"
      - "C:/Program Files/CMake/bin/ninja.com"
      - "C:/Program Files/CMake/bin/ninja.exe"
      - "C:/Program Files/CMake/bin/ninja"
      - "C:/Program Files/CMake/bin/samu.com"
      - "C:/Program Files/CMake/bin/samu.exe"
      - "C:/Program Files/CMake/bin/samu"
      - "C:/Ninja/ninja-build.com"
      - "C:/Ninja/ninja-build.exe"
      - "C:/Ninja/ninja-build"
      - "C:/Ninja/ninja.com"
    found: "C:/Ninja/ninja.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/MinGW/bin/g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        C:/Users/<USER>/OneDrive/Desktop/C++/automata/build/CMakeFiles/4.1.1/CompilerIdCXX/a.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "C:/MinGW/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
    searched_directories:
      - "C:/MinGW/bin/ar.com"
    found: "C:/MinGW/bin/ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "C:/MinGW/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
    searched_directories:
      - "C:/MinGW/bin/ranlib.com"
    found: "C:/MinGW/bin/ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "C:/MinGW/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
    searched_directories:
      - "C:/MinGW/bin/strip.com"
    found: "C:/MinGW/bin/strip.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "C:/MinGW/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
    searched_directories:
      - "C:/MinGW/bin/ld.com"
    found: "C:/MinGW/bin/ld.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "C:/MinGW/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
    searched_directories:
      - "C:/MinGW/bin/nm.com"
    found: "C:/MinGW/bin/nm.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "C:/MinGW/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
    searched_directories:
      - "C:/MinGW/bin/objdump.com"
    found: "C:/MinGW/bin/objdump.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "C:/MinGW/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
    searched_directories:
      - "C:/MinGW/bin/objcopy.com"
    found: "C:/MinGW/bin/objcopy.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "C:/MinGW/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
    searched_directories:
      - "C:/MinGW/bin/readelf.com"
    found: "C:/MinGW/bin/readelf.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "C:/MinGW/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
    searched_directories:
      - "C:/MinGW/bin/dlltool.com"
    found: "C:/MinGW/bin/dlltool.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "C:/MinGW/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
    searched_directories:
      - "C:/MinGW/bin/addr2line.com"
    found: "C:/MinGW/bin/addr2line.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "C:/MinGW/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
    searched_directories:
      - "C:/MinGW/bin/tapi.com"
      - "C:/MinGW/bin/tapi.exe"
      - "C:/MinGW/bin/tapi"
      - "C:/Python312/Scripts/tapi.com"
      - "C:/Python312/Scripts/tapi.exe"
      - "C:/Python312/Scripts/tapi"
      - "C:/Python312/tapi.com"
      - "C:/Python312/tapi.exe"
      - "C:/Python312/tapi"
      - "C:/Program Files/Oculus/Support/oculus-runtime/tapi.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/tapi.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/tapi"
      - "C:/Windows/System32/tapi.com"
      - "C:/Windows/System32/tapi.exe"
      - "C:/Windows/System32/tapi"
      - "C:/Windows/tapi.com"
      - "C:/Windows/tapi.exe"
      - "C:/Windows/tapi"
      - "C:/Windows/System32/wbem/tapi.com"
      - "C:/Windows/System32/wbem/tapi.exe"
      - "C:/Windows/System32/wbem/tapi"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi"
      - "C:/Windows/System32/OpenSSH/tapi.com"
      - "C:/Windows/System32/OpenSSH/tapi.exe"
      - "C:/Windows/System32/OpenSSH/tapi"
      - "C:/Program Files/Git/cmd/tapi.com"
      - "C:/Program Files/Git/cmd/tapi.exe"
      - "C:/Program Files/Git/cmd/tapi"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/tapi.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/tapi.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/tapi"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi"
      - "C:/Program Files/dotnet/tapi.com"
      - "C:/Program Files/dotnet/tapi.exe"
      - "C:/Program Files/dotnet/tapi"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/tapi.com"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/tapi.exe"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/tapi"
      - "C:/Program Files/nodejs/tapi.com"
      - "C:/Program Files/nodejs/tapi.exe"
      - "C:/Program Files/nodejs/tapi"
      - "C:/ProgramData/chocolatey/bin/tapi.com"
      - "C:/ProgramData/chocolatey/bin/tapi.exe"
      - "C:/ProgramData/chocolatey/bin/tapi"
      - "C:/Program Files/Docker/Docker/resources/bin/tapi.com"
      - "C:/Program Files/Docker/Docker/resources/bin/tapi.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/tapi"
      - "C:/Program Files/Graphviz/bin/tapi.com"
      - "C:/Program Files/Graphviz/bin/tapi.exe"
      - "C:/Program Files/Graphviz/bin/tapi"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/tapi.com"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/tapi.exe"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/tapi"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/tapi.com"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/tapi.exe"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/tapi"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/tapi.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/tapi.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/tapi"
      - "C:/Program Files/CMake/bin/tapi.com"
      - "C:/Program Files/CMake/bin/tapi.exe"
      - "C:/Program Files/CMake/bin/tapi"
      - "C:/Ninja/tapi.com"
      - "C:/Ninja/tapi.exe"
      - "C:/Ninja/tapi"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/tapi"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi"
      - "C:/ghcup/bin/tapi.com"
      - "C:/ghcup/bin/tapi.exe"
      - "C:/ghcup/bin/tapi"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/tapi.com"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/tapi.exe"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-6.3"
      - "gcc-ar-6"
      - "gcc-ar6"
      - "gcc-ar"
    candidate_directories:
      - "C:/MinGW/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
    searched_directories:
      - "C:/MinGW/bin/gcc-ar-6.3.com"
      - "C:/MinGW/bin/gcc-ar-6.3.exe"
      - "C:/MinGW/bin/gcc-ar-6.3"
      - "C:/Python312/Scripts/gcc-ar-6.3.com"
      - "C:/Python312/Scripts/gcc-ar-6.3.exe"
      - "C:/Python312/Scripts/gcc-ar-6.3"
      - "C:/Python312/gcc-ar-6.3.com"
      - "C:/Python312/gcc-ar-6.3.exe"
      - "C:/Python312/gcc-ar-6.3"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ar-6.3.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ar-6.3.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ar-6.3"
      - "C:/Windows/System32/gcc-ar-6.3.com"
      - "C:/Windows/System32/gcc-ar-6.3.exe"
      - "C:/Windows/System32/gcc-ar-6.3"
      - "C:/Windows/gcc-ar-6.3.com"
      - "C:/Windows/gcc-ar-6.3.exe"
      - "C:/Windows/gcc-ar-6.3"
      - "C:/Windows/System32/wbem/gcc-ar-6.3.com"
      - "C:/Windows/System32/wbem/gcc-ar-6.3.exe"
      - "C:/Windows/System32/wbem/gcc-ar-6.3"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-6.3.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-6.3.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-6.3"
      - "C:/Windows/System32/OpenSSH/gcc-ar-6.3.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-6.3.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-6.3"
      - "C:/Program Files/Git/cmd/gcc-ar-6.3.com"
      - "C:/Program Files/Git/cmd/gcc-ar-6.3.exe"
      - "C:/Program Files/Git/cmd/gcc-ar-6.3"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-6.3.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-6.3.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-6.3"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-6.3.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-6.3.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-6.3"
      - "C:/Program Files/dotnet/gcc-ar-6.3.com"
      - "C:/Program Files/dotnet/gcc-ar-6.3.exe"
      - "C:/Program Files/dotnet/gcc-ar-6.3"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ar-6.3.com"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ar-6.3.exe"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ar-6.3"
      - "C:/Program Files/nodejs/gcc-ar-6.3.com"
      - "C:/Program Files/nodejs/gcc-ar-6.3.exe"
      - "C:/Program Files/nodejs/gcc-ar-6.3"
      - "C:/ProgramData/chocolatey/bin/gcc-ar-6.3.com"
      - "C:/ProgramData/chocolatey/bin/gcc-ar-6.3.exe"
      - "C:/ProgramData/chocolatey/bin/gcc-ar-6.3"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ar-6.3.com"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ar-6.3.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ar-6.3"
      - "C:/Program Files/Graphviz/bin/gcc-ar-6.3.com"
      - "C:/Program Files/Graphviz/bin/gcc-ar-6.3.exe"
      - "C:/Program Files/Graphviz/bin/gcc-ar-6.3"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ar-6.3.com"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ar-6.3.exe"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ar-6.3"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ar-6.3.com"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ar-6.3.exe"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ar-6.3"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-6.3.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-6.3.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-6.3"
      - "C:/Program Files/CMake/bin/gcc-ar-6.3.com"
      - "C:/Program Files/CMake/bin/gcc-ar-6.3.exe"
      - "C:/Program Files/CMake/bin/gcc-ar-6.3"
      - "C:/Ninja/gcc-ar-6.3.com"
      - "C:/Ninja/gcc-ar-6.3.exe"
      - "C:/Ninja/gcc-ar-6.3"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-6.3.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-6.3.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-6.3"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-6.3.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-6.3.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-6.3"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ar-6.3.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ar-6.3.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ar-6.3"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-6.3.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-6.3.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-6.3"
      - "C:/ghcup/bin/gcc-ar-6.3.com"
      - "C:/ghcup/bin/gcc-ar-6.3.exe"
      - "C:/ghcup/bin/gcc-ar-6.3"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ar-6.3.com"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ar-6.3.exe"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ar-6.3"
      - "C:/MinGW/bin/gcc-ar-6.com"
      - "C:/MinGW/bin/gcc-ar-6.exe"
      - "C:/MinGW/bin/gcc-ar-6"
      - "C:/Python312/Scripts/gcc-ar-6.com"
      - "C:/Python312/Scripts/gcc-ar-6.exe"
      - "C:/Python312/Scripts/gcc-ar-6"
      - "C:/Python312/gcc-ar-6.com"
      - "C:/Python312/gcc-ar-6.exe"
      - "C:/Python312/gcc-ar-6"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ar-6.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ar-6.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ar-6"
      - "C:/Windows/System32/gcc-ar-6.com"
      - "C:/Windows/System32/gcc-ar-6.exe"
      - "C:/Windows/System32/gcc-ar-6"
      - "C:/Windows/gcc-ar-6.com"
      - "C:/Windows/gcc-ar-6.exe"
      - "C:/Windows/gcc-ar-6"
      - "C:/Windows/System32/wbem/gcc-ar-6.com"
      - "C:/Windows/System32/wbem/gcc-ar-6.exe"
      - "C:/Windows/System32/wbem/gcc-ar-6"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-6.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-6.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-6"
      - "C:/Windows/System32/OpenSSH/gcc-ar-6.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-6.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-6"
      - "C:/Program Files/Git/cmd/gcc-ar-6.com"
      - "C:/Program Files/Git/cmd/gcc-ar-6.exe"
      - "C:/Program Files/Git/cmd/gcc-ar-6"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-6.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-6.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar-6"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-6.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-6.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-6"
      - "C:/Program Files/dotnet/gcc-ar-6.com"
      - "C:/Program Files/dotnet/gcc-ar-6.exe"
      - "C:/Program Files/dotnet/gcc-ar-6"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ar-6.com"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ar-6.exe"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ar-6"
      - "C:/Program Files/nodejs/gcc-ar-6.com"
      - "C:/Program Files/nodejs/gcc-ar-6.exe"
      - "C:/Program Files/nodejs/gcc-ar-6"
      - "C:/ProgramData/chocolatey/bin/gcc-ar-6.com"
      - "C:/ProgramData/chocolatey/bin/gcc-ar-6.exe"
      - "C:/ProgramData/chocolatey/bin/gcc-ar-6"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ar-6.com"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ar-6.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ar-6"
      - "C:/Program Files/Graphviz/bin/gcc-ar-6.com"
      - "C:/Program Files/Graphviz/bin/gcc-ar-6.exe"
      - "C:/Program Files/Graphviz/bin/gcc-ar-6"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ar-6.com"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ar-6.exe"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ar-6"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ar-6.com"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ar-6.exe"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ar-6"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-6.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-6.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-6"
      - "C:/Program Files/CMake/bin/gcc-ar-6.com"
      - "C:/Program Files/CMake/bin/gcc-ar-6.exe"
      - "C:/Program Files/CMake/bin/gcc-ar-6"
      - "C:/Ninja/gcc-ar-6.com"
      - "C:/Ninja/gcc-ar-6.exe"
      - "C:/Ninja/gcc-ar-6"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-6.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-6.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-6"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-6.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-6.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-6"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ar-6.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ar-6.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ar-6"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-6.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-6.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-6"
      - "C:/ghcup/bin/gcc-ar-6.com"
      - "C:/ghcup/bin/gcc-ar-6.exe"
      - "C:/ghcup/bin/gcc-ar-6"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ar-6.com"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ar-6.exe"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ar-6"
      - "C:/MinGW/bin/gcc-ar6.com"
      - "C:/MinGW/bin/gcc-ar6.exe"
      - "C:/MinGW/bin/gcc-ar6"
      - "C:/Python312/Scripts/gcc-ar6.com"
      - "C:/Python312/Scripts/gcc-ar6.exe"
      - "C:/Python312/Scripts/gcc-ar6"
      - "C:/Python312/gcc-ar6.com"
      - "C:/Python312/gcc-ar6.exe"
      - "C:/Python312/gcc-ar6"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ar6.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ar6.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ar6"
      - "C:/Windows/System32/gcc-ar6.com"
      - "C:/Windows/System32/gcc-ar6.exe"
      - "C:/Windows/System32/gcc-ar6"
      - "C:/Windows/gcc-ar6.com"
      - "C:/Windows/gcc-ar6.exe"
      - "C:/Windows/gcc-ar6"
      - "C:/Windows/System32/wbem/gcc-ar6.com"
      - "C:/Windows/System32/wbem/gcc-ar6.exe"
      - "C:/Windows/System32/wbem/gcc-ar6"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar6.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar6.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar6"
      - "C:/Windows/System32/OpenSSH/gcc-ar6.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar6.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar6"
      - "C:/Program Files/Git/cmd/gcc-ar6.com"
      - "C:/Program Files/Git/cmd/gcc-ar6.exe"
      - "C:/Program Files/Git/cmd/gcc-ar6"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar6.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar6.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ar6"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar6.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar6.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar6"
      - "C:/Program Files/dotnet/gcc-ar6.com"
      - "C:/Program Files/dotnet/gcc-ar6.exe"
      - "C:/Program Files/dotnet/gcc-ar6"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ar6.com"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ar6.exe"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ar6"
      - "C:/Program Files/nodejs/gcc-ar6.com"
      - "C:/Program Files/nodejs/gcc-ar6.exe"
      - "C:/Program Files/nodejs/gcc-ar6"
      - "C:/ProgramData/chocolatey/bin/gcc-ar6.com"
      - "C:/ProgramData/chocolatey/bin/gcc-ar6.exe"
      - "C:/ProgramData/chocolatey/bin/gcc-ar6"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ar6.com"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ar6.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ar6"
      - "C:/Program Files/Graphviz/bin/gcc-ar6.com"
      - "C:/Program Files/Graphviz/bin/gcc-ar6.exe"
      - "C:/Program Files/Graphviz/bin/gcc-ar6"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ar6.com"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ar6.exe"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ar6"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ar6.com"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ar6.exe"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ar6"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar6.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar6.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar6"
      - "C:/Program Files/CMake/bin/gcc-ar6.com"
      - "C:/Program Files/CMake/bin/gcc-ar6.exe"
      - "C:/Program Files/CMake/bin/gcc-ar6"
      - "C:/Ninja/gcc-ar6.com"
      - "C:/Ninja/gcc-ar6.exe"
      - "C:/Ninja/gcc-ar6"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar6.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar6.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar6"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar6.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar6.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar6"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ar6.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ar6.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ar6"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar6.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar6.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar6"
      - "C:/ghcup/bin/gcc-ar6.com"
      - "C:/ghcup/bin/gcc-ar6.exe"
      - "C:/ghcup/bin/gcc-ar6"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ar6.com"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ar6.exe"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ar6"
      - "C:/MinGW/bin/gcc-ar.com"
    found: "C:/MinGW/bin/gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-6.3"
      - "gcc-ranlib-6"
      - "gcc-ranlib6"
      - "gcc-ranlib"
    candidate_directories:
      - "C:/MinGW/bin/"
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
    searched_directories:
      - "C:/MinGW/bin/gcc-ranlib-6.3.com"
      - "C:/MinGW/bin/gcc-ranlib-6.3.exe"
      - "C:/MinGW/bin/gcc-ranlib-6.3"
      - "C:/Python312/Scripts/gcc-ranlib-6.3.com"
      - "C:/Python312/Scripts/gcc-ranlib-6.3.exe"
      - "C:/Python312/Scripts/gcc-ranlib-6.3"
      - "C:/Python312/gcc-ranlib-6.3.com"
      - "C:/Python312/gcc-ranlib-6.3.exe"
      - "C:/Python312/gcc-ranlib-6.3"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ranlib-6.3.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ranlib-6.3.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ranlib-6.3"
      - "C:/Windows/System32/gcc-ranlib-6.3.com"
      - "C:/Windows/System32/gcc-ranlib-6.3.exe"
      - "C:/Windows/System32/gcc-ranlib-6.3"
      - "C:/Windows/gcc-ranlib-6.3.com"
      - "C:/Windows/gcc-ranlib-6.3.exe"
      - "C:/Windows/gcc-ranlib-6.3"
      - "C:/Windows/System32/wbem/gcc-ranlib-6.3.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-6.3.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-6.3"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-6.3.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-6.3.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-6.3"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-6.3.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-6.3.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-6.3"
      - "C:/Program Files/Git/cmd/gcc-ranlib-6.3.com"
      - "C:/Program Files/Git/cmd/gcc-ranlib-6.3.exe"
      - "C:/Program Files/Git/cmd/gcc-ranlib-6.3"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-6.3.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-6.3.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-6.3"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-6.3.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-6.3.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-6.3"
      - "C:/Program Files/dotnet/gcc-ranlib-6.3.com"
      - "C:/Program Files/dotnet/gcc-ranlib-6.3.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-6.3"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ranlib-6.3.com"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ranlib-6.3.exe"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ranlib-6.3"
      - "C:/Program Files/nodejs/gcc-ranlib-6.3.com"
      - "C:/Program Files/nodejs/gcc-ranlib-6.3.exe"
      - "C:/Program Files/nodejs/gcc-ranlib-6.3"
      - "C:/ProgramData/chocolatey/bin/gcc-ranlib-6.3.com"
      - "C:/ProgramData/chocolatey/bin/gcc-ranlib-6.3.exe"
      - "C:/ProgramData/chocolatey/bin/gcc-ranlib-6.3"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ranlib-6.3.com"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ranlib-6.3.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ranlib-6.3"
      - "C:/Program Files/Graphviz/bin/gcc-ranlib-6.3.com"
      - "C:/Program Files/Graphviz/bin/gcc-ranlib-6.3.exe"
      - "C:/Program Files/Graphviz/bin/gcc-ranlib-6.3"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ranlib-6.3.com"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ranlib-6.3.exe"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ranlib-6.3"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ranlib-6.3.com"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ranlib-6.3.exe"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ranlib-6.3"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-6.3.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-6.3.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-6.3"
      - "C:/Program Files/CMake/bin/gcc-ranlib-6.3.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib-6.3.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib-6.3"
      - "C:/Ninja/gcc-ranlib-6.3.com"
      - "C:/Ninja/gcc-ranlib-6.3.exe"
      - "C:/Ninja/gcc-ranlib-6.3"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-6.3.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-6.3.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-6.3"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-6.3.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-6.3.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-6.3"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ranlib-6.3.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ranlib-6.3.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ranlib-6.3"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-6.3.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-6.3.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-6.3"
      - "C:/ghcup/bin/gcc-ranlib-6.3.com"
      - "C:/ghcup/bin/gcc-ranlib-6.3.exe"
      - "C:/ghcup/bin/gcc-ranlib-6.3"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ranlib-6.3.com"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ranlib-6.3.exe"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ranlib-6.3"
      - "C:/MinGW/bin/gcc-ranlib-6.com"
      - "C:/MinGW/bin/gcc-ranlib-6.exe"
      - "C:/MinGW/bin/gcc-ranlib-6"
      - "C:/Python312/Scripts/gcc-ranlib-6.com"
      - "C:/Python312/Scripts/gcc-ranlib-6.exe"
      - "C:/Python312/Scripts/gcc-ranlib-6"
      - "C:/Python312/gcc-ranlib-6.com"
      - "C:/Python312/gcc-ranlib-6.exe"
      - "C:/Python312/gcc-ranlib-6"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ranlib-6.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ranlib-6.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ranlib-6"
      - "C:/Windows/System32/gcc-ranlib-6.com"
      - "C:/Windows/System32/gcc-ranlib-6.exe"
      - "C:/Windows/System32/gcc-ranlib-6"
      - "C:/Windows/gcc-ranlib-6.com"
      - "C:/Windows/gcc-ranlib-6.exe"
      - "C:/Windows/gcc-ranlib-6"
      - "C:/Windows/System32/wbem/gcc-ranlib-6.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-6.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-6"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-6.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-6.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-6"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-6.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-6.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-6"
      - "C:/Program Files/Git/cmd/gcc-ranlib-6.com"
      - "C:/Program Files/Git/cmd/gcc-ranlib-6.exe"
      - "C:/Program Files/Git/cmd/gcc-ranlib-6"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-6.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-6.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib-6"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-6.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-6.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-6"
      - "C:/Program Files/dotnet/gcc-ranlib-6.com"
      - "C:/Program Files/dotnet/gcc-ranlib-6.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-6"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ranlib-6.com"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ranlib-6.exe"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ranlib-6"
      - "C:/Program Files/nodejs/gcc-ranlib-6.com"
      - "C:/Program Files/nodejs/gcc-ranlib-6.exe"
      - "C:/Program Files/nodejs/gcc-ranlib-6"
      - "C:/ProgramData/chocolatey/bin/gcc-ranlib-6.com"
      - "C:/ProgramData/chocolatey/bin/gcc-ranlib-6.exe"
      - "C:/ProgramData/chocolatey/bin/gcc-ranlib-6"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ranlib-6.com"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ranlib-6.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ranlib-6"
      - "C:/Program Files/Graphviz/bin/gcc-ranlib-6.com"
      - "C:/Program Files/Graphviz/bin/gcc-ranlib-6.exe"
      - "C:/Program Files/Graphviz/bin/gcc-ranlib-6"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ranlib-6.com"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ranlib-6.exe"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ranlib-6"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ranlib-6.com"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ranlib-6.exe"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ranlib-6"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-6.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-6.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-6"
      - "C:/Program Files/CMake/bin/gcc-ranlib-6.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib-6.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib-6"
      - "C:/Ninja/gcc-ranlib-6.com"
      - "C:/Ninja/gcc-ranlib-6.exe"
      - "C:/Ninja/gcc-ranlib-6"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-6.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-6.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-6"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-6.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-6.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-6"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ranlib-6.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ranlib-6.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ranlib-6"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-6.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-6.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-6"
      - "C:/ghcup/bin/gcc-ranlib-6.com"
      - "C:/ghcup/bin/gcc-ranlib-6.exe"
      - "C:/ghcup/bin/gcc-ranlib-6"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ranlib-6.com"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ranlib-6.exe"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ranlib-6"
      - "C:/MinGW/bin/gcc-ranlib6.com"
      - "C:/MinGW/bin/gcc-ranlib6.exe"
      - "C:/MinGW/bin/gcc-ranlib6"
      - "C:/Python312/Scripts/gcc-ranlib6.com"
      - "C:/Python312/Scripts/gcc-ranlib6.exe"
      - "C:/Python312/Scripts/gcc-ranlib6"
      - "C:/Python312/gcc-ranlib6.com"
      - "C:/Python312/gcc-ranlib6.exe"
      - "C:/Python312/gcc-ranlib6"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ranlib6.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ranlib6.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/gcc-ranlib6"
      - "C:/Windows/System32/gcc-ranlib6.com"
      - "C:/Windows/System32/gcc-ranlib6.exe"
      - "C:/Windows/System32/gcc-ranlib6"
      - "C:/Windows/gcc-ranlib6.com"
      - "C:/Windows/gcc-ranlib6.exe"
      - "C:/Windows/gcc-ranlib6"
      - "C:/Windows/System32/wbem/gcc-ranlib6.com"
      - "C:/Windows/System32/wbem/gcc-ranlib6.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib6"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib6.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib6.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib6"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib6.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib6.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib6"
      - "C:/Program Files/Git/cmd/gcc-ranlib6.com"
      - "C:/Program Files/Git/cmd/gcc-ranlib6.exe"
      - "C:/Program Files/Git/cmd/gcc-ranlib6"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib6.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib6.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/gcc-ranlib6"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib6.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib6.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib6"
      - "C:/Program Files/dotnet/gcc-ranlib6.com"
      - "C:/Program Files/dotnet/gcc-ranlib6.exe"
      - "C:/Program Files/dotnet/gcc-ranlib6"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ranlib6.com"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ranlib6.exe"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/gcc-ranlib6"
      - "C:/Program Files/nodejs/gcc-ranlib6.com"
      - "C:/Program Files/nodejs/gcc-ranlib6.exe"
      - "C:/Program Files/nodejs/gcc-ranlib6"
      - "C:/ProgramData/chocolatey/bin/gcc-ranlib6.com"
      - "C:/ProgramData/chocolatey/bin/gcc-ranlib6.exe"
      - "C:/ProgramData/chocolatey/bin/gcc-ranlib6"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ranlib6.com"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ranlib6.exe"
      - "C:/Program Files/Docker/Docker/resources/bin/gcc-ranlib6"
      - "C:/Program Files/Graphviz/bin/gcc-ranlib6.com"
      - "C:/Program Files/Graphviz/bin/gcc-ranlib6.exe"
      - "C:/Program Files/Graphviz/bin/gcc-ranlib6"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ranlib6.com"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ranlib6.exe"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/gcc-ranlib6"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ranlib6.com"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ranlib6.exe"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/gcc-ranlib6"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib6.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib6.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib6"
      - "C:/Program Files/CMake/bin/gcc-ranlib6.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib6.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib6"
      - "C:/Ninja/gcc-ranlib6.com"
      - "C:/Ninja/gcc-ranlib6.exe"
      - "C:/Ninja/gcc-ranlib6"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib6.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib6.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib6"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib6.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib6.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib6"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ranlib6.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ranlib6.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/gcc-ranlib6"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib6.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib6.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib6"
      - "C:/ghcup/bin/gcc-ranlib6.com"
      - "C:/ghcup/bin/gcc-ranlib6.exe"
      - "C:/ghcup/bin/gcc-ranlib6"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ranlib6.com"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ranlib6.exe"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/gcc-ranlib6"
      - "C:/MinGW/bin/gcc-ranlib.com"
    found: "C:/MinGW/bin/gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake:167 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX.cmake:2 (__windows_compiler_gnu)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXInformation.cmake:48 (include)"
      - "CMakeLists.txt:3 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "windres"
      - "windres"
    candidate_directories:
      - "C:/Python312/Scripts/"
      - "C:/Python312/"
      - "C:/Program Files/Oculus/Support/oculus-runtime/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/dotnet/"
      - "C:/MinGW/bin/"
      - "C:/Program Files/ffmpeg-6.1.1-essentials_build/bin/"
      - "C:/Program Files/nodejs/"
      - "C:/ProgramData/chocolatey/bin/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "C:/Program Files/Graphviz/bin/"
      - "C:/maven/apache-maven-3.9.9-bin/apache-maven-3.9.9/bin/"
      - "C:/Users/<USER>/.jdks/corretto-17.0.8/bin/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Ninja/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WinGet/Packages/Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "C:/ghcup/bin/"
      - "C:/Program Files/JetBrains/IntelliJ IDEA Community Edition 2024.3/bin/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/automata/bin/"
      - "C:/Program Files (x86)/automata/sbin/"
      - "C:/Program Files (x86)/automata/"
    searched_directories:
      - "C:/Python312/Scripts/windres.com"
      - "C:/Python312/Scripts/windres.exe"
      - "C:/Python312/Scripts/windres"
      - "C:/Python312/windres.com"
      - "C:/Python312/windres.exe"
      - "C:/Python312/windres"
      - "C:/Program Files/Oculus/Support/oculus-runtime/windres.com"
      - "C:/Program Files/Oculus/Support/oculus-runtime/windres.exe"
      - "C:/Program Files/Oculus/Support/oculus-runtime/windres"
      - "C:/Windows/System32/windres.com"
      - "C:/Windows/System32/windres.exe"
      - "C:/Windows/System32/windres"
      - "C:/Windows/windres.com"
      - "C:/Windows/windres.exe"
      - "C:/Windows/windres"
      - "C:/Windows/System32/wbem/windres.com"
      - "C:/Windows/System32/wbem/windres.exe"
      - "C:/Windows/System32/wbem/windres"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/windres.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/windres.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/windres"
      - "C:/Windows/System32/OpenSSH/windres.com"
      - "C:/Windows/System32/OpenSSH/windres.exe"
      - "C:/Windows/System32/OpenSSH/windres"
      - "C:/Program Files/Git/cmd/windres.com"
      - "C:/Program Files/Git/cmd/windres.exe"
      - "C:/Program Files/Git/cmd/windres"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/windres.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/windres.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/windres"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/windres.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/windres.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/windres"
      - "C:/Program Files/dotnet/windres.com"
      - "C:/Program Files/dotnet/windres.exe"
      - "C:/Program Files/dotnet/windres"
      - "C:/MinGW/bin/windres.com"
    found: "C:/MinGW/bin/windres.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Python312\\Scripts\\"
        - "C:\\Python312\\"
        - "C:\\Program Files\\Oculus\\Support\\oculus-runtime"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\MinGW\\bin"
        - "C:\\Program Files\\ffmpeg-6.1.1-essentials_build\\bin"
        - "C:\\Program Files\\nodejs\\"
        - "C:\\ProgramData\\chocolatey\\bin"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "C:\\Program Files\\Graphviz\\bin"
        - "C:\\maven\\apache-maven-3.9.9-bin\\apache-maven-3.9.9\\bin"
        - "C:\\Users\\<USER>\\.jdks\\corretto-17.0.8\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Ninja"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\ghcup\\bin"
        - "C:\\Program Files\\JetBrains\\IntelliJ IDEA Community Edition 2024.3\\bin"
        - "C:\\Ninja"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/automata"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/automata"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/C++/automata/build/CMakeFiles/CMakeScratch/TryCompile-h5c34e"
      binary: "C:/Users/<USER>/OneDrive/Desktop/C++/automata/build/CMakeFiles/CMakeScratch/TryCompile-h5c34e"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/C++/automata/build/CMakeFiles/CMakeScratch/TryCompile-h5c34e'
        
        Run Build Command(s): C:/Ninja/ninja.exe -v cmTC_63431
        [1/2] C:\\MinGW\\bin\\g++.exe   -v -o CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj -c "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp"
        Using built-in specs.
        COLLECT_GCC=C:\\MinGW\\bin\\g++.exe
        Target: mingw32
        Configured with: ../src/gcc-6.3.0/configure --build=x86_64-pc-linux-gnu --host=mingw32 --with-gmp=/mingw --with-mpfr=/mingw --with-mpc=/mingw --with-isl=/mingw --prefix=/mingw --disable-win32-registry --target=mingw32 --with-arch=i586 --enable-languages=c,c++,objc,obj-c++,fortran,ada --with-pkgversion='MinGW.org GCC-6.3.0-1' --enable-static --enable-shared --enable-threads --with-dwarf2 --disable-sjlj-exceptions --enable-version-specific-runtime-libs --with-libiconv-prefix=/mingw --with-libintl-prefix=/mingw --enable-libstdcxx-debug --with-tune=generic --enable-libgomp --disable-libvtv --enable-nls
        Thread model: win32
        gcc version 6.3.0 (MinGW.org GCC-6.3.0-1) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=i586'
         c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/cc1plus.exe -quiet -v -iprefix c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/ C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=i586 -auxbase-strip CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccijoLTm.s
        GNU C++14 (MinGW.org GCC-6.3.0-1) version 6.3.0 (mingw32)
        	compiled by GNU C version 6.3.0, GMP version 6.1.2, MPFR version 3.1.5, MPC version 1.0.2, isl version 0.15
        warning: MPC header version 1.0.2 differs from library version 1.0.3.
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/../../../../mingw32/include"
        ignoring duplicate directory "c:/mingw/lib/gcc/../../lib/gcc/mingw32/6.3.0/include/c++"
        ignoring duplicate directory "c:/mingw/lib/gcc/../../lib/gcc/mingw32/6.3.0/include/c++/mingw32"
        ignoring duplicate directory "c:/mingw/lib/gcc/../../lib/gcc/mingw32/6.3.0/include/c++/backward"
        ignoring duplicate directory "c:/mingw/lib/gcc/../../lib/gcc/mingw32/6.3.0/include"
        ignoring duplicate directory "/mingw/lib/gcc/mingw32/6.3.0/../../../../include"
        ignoring duplicate directory "c:/mingw/lib/gcc/../../include"
        ignoring duplicate directory "c:/mingw/lib/gcc/../../lib/gcc/mingw32/6.3.0/include-fixed"
        ignoring nonexistent directory "c:/mingw/lib/gcc/../../lib/gcc/mingw32/6.3.0/../../../../mingw32/include"
        ignoring duplicate directory "/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/include/c++
         c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/include/c++/mingw32
         c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/include/c++/backward
         c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/include
         c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/../../../../include
         c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/include-fixed
        End of search list.
        GNU C++14 (MinGW.org GCC-6.3.0-1) version 6.3.0 (mingw32)
        	compiled by GNU C version 6.3.0, GMP version 6.1.2, MPFR version 3.1.5, MPC version 1.0.2, isl version 0.15
        warning: MPC header version 1.0.2 differs from library version 1.0.3.
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: af09a87986453bf79da3565f33c7648f
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=i586'
         c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/bin/as.exe -v -o CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccijoLTm.s
        GNU assembler version 2.28 (mingw32) using BFD version (GNU Binutils) 2.28
        COMPILER_PATH=c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/;c:/mingw/bin/../libexec/gcc/;c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/bin/
        LIBRARY_PATH=c:/mingw/bin/../lib/gcc/mingw32/6.3.0/;c:/mingw/bin/../lib/gcc/;c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/lib/;c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=i586'
        [2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && C:\\MinGW\\bin\\g++.exe  -v -Wl,-v CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_63431.exe -Wl,--out-implib,libcmTC_63431.dll.a -Wl,--major-image-version,0,--minor-image-version,0   && cd ."
        Using built-in specs.
        COLLECT_GCC=C:\\MinGW\\bin\\g++.exe
        COLLECT_LTO_WRAPPER=c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/lto-wrapper.exe
        Target: mingw32
        Configured with: ../src/gcc-6.3.0/configure --build=x86_64-pc-linux-gnu --host=mingw32 --with-gmp=/mingw --with-mpfr=/mingw --with-mpc=/mingw --with-isl=/mingw --prefix=/mingw --disable-win32-registry --target=mingw32 --with-arch=i586 --enable-languages=c,c++,objc,obj-c++,fortran,ada --with-pkgversion='MinGW.org GCC-6.3.0-1' --enable-static --enable-shared --enable-threads --with-dwarf2 --disable-sjlj-exceptions --enable-version-specific-runtime-libs --with-libiconv-prefix=/mingw --with-libintl-prefix=/mingw --enable-libstdcxx-debug --with-tune=generic --enable-libgomp --disable-libvtv --enable-nls
        Thread model: win32
        gcc version 6.3.0 (MinGW.org GCC-6.3.0-1) 
        COMPILER_PATH=c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/;c:/mingw/bin/../libexec/gcc/;c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/bin/
        LIBRARY_PATH=c:/mingw/bin/../lib/gcc/mingw32/6.3.0/;c:/mingw/bin/../lib/gcc/;c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/lib/;c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_63431.exe' '-shared-libgcc' '-mtune=generic' '-march=i586'
         c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/collect2.exe -plugin c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/liblto_plugin-0.dll -plugin-opt=c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8jfa2v.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -Bdynamic -u ___register_frame_info -u ___deregister_frame_info -o cmTC_63431.exe c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../crt2.o c:/mingw/bin/../lib/gcc/mingw32/6.3.0/crtbegin.o -Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0 -Lc:/mingw/bin/../lib/gcc -Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/lib -Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../.. -v CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_63431.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt c:/mingw/bin/../lib/gcc/mingw32/6.3.0/crtend.o
        collect2 version 6.3.0
        c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/bin/ld.exe -plugin c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/liblto_plugin-0.dll -plugin-opt=c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8jfa2v.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -Bdynamic -u ___register_frame_info -u ___deregister_frame_info -o cmTC_63431.exe c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../crt2.o c:/mingw/bin/../lib/gcc/mingw32/6.3.0/crtbegin.o -Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0 -Lc:/mingw/bin/../lib/gcc -Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/lib -Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../.. -v CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_63431.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt c:/mingw/bin/../lib/gcc/mingw32/6.3.0/crtend.o
        GNU ld (GNU Binutils) 2.28
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_63431.exe' '-shared-libgcc' '-mtune=generic' '-march=i586'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/include/c++]
          add: [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/include/c++/mingw32]
          add: [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/include/c++/backward]
          add: [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/include]
          add: [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../include]
          add: [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/include-fixed]
        end of search list found
        collapse include dir [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/include/c++] ==> [C:/MinGW/lib/gcc/mingw32/6.3.0/include/c++]
        collapse include dir [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/include/c++/mingw32] ==> [C:/MinGW/lib/gcc/mingw32/6.3.0/include/c++/mingw32]
        collapse include dir [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/include/c++/backward] ==> [C:/MinGW/lib/gcc/mingw32/6.3.0/include/c++/backward]
        collapse include dir [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/include] ==> [C:/MinGW/lib/gcc/mingw32/6.3.0/include]
        collapse include dir [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../include] ==> [C:/MinGW/include]
        collapse include dir [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/include-fixed] ==> [C:/MinGW/lib/gcc/mingw32/6.3.0/include-fixed]
        implicit include dirs: [C:/MinGW/lib/gcc/mingw32/6.3.0/include/c++;C:/MinGW/lib/gcc/mingw32/6.3.0/include/c++/mingw32;C:/MinGW/lib/gcc/mingw32/6.3.0/include/c++/backward;C:/MinGW/lib/gcc/mingw32/6.3.0/include;C:/MinGW/include;C:/MinGW/lib/gcc/mingw32/6.3.0/include-fixed]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/C++/automata/build/CMakeFiles/CMakeScratch/TryCompile-h5c34e']
        ignore line: []
        ignore line: [Run Build Command(s): C:/Ninja/ninja.exe -v cmTC_63431]
        ignore line: [[1/2] C:\\MinGW\\bin\\g++.exe   -v -o CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj -c "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\MinGW\\bin\\g++.exe]
        ignore line: [Target: mingw32]
        ignore line: [Configured with: ../src/gcc-6.3.0/configure --build=x86_64-pc-linux-gnu --host=mingw32 --with-gmp=/mingw --with-mpfr=/mingw --with-mpc=/mingw --with-isl=/mingw --prefix=/mingw --disable-win32-registry --target=mingw32 --with-arch=i586 --enable-languages=c c++ objc obj-c++ fortran ada --with-pkgversion='MinGW.org GCC-6.3.0-1' --enable-static --enable-shared --enable-threads --with-dwarf2 --disable-sjlj-exceptions --enable-version-specific-runtime-libs --with-libiconv-prefix=/mingw --with-libintl-prefix=/mingw --enable-libstdcxx-debug --with-tune=generic --enable-libgomp --disable-libvtv --enable-nls]
        ignore line: [Thread model: win32]
        ignore line: [gcc version 6.3.0 (MinGW.org GCC-6.3.0-1) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=i586']
        ignore line: [ c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/cc1plus.exe -quiet -v -iprefix c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/ C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=i586 -auxbase-strip CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccijoLTm.s]
        ignore line: [GNU C++14 (MinGW.org GCC-6.3.0-1) version 6.3.0 (mingw32)]
        ignore line: [	compiled by GNU C version 6.3.0  GMP version 6.1.2  MPFR version 3.1.5  MPC version 1.0.2  isl version 0.15]
        ignore line: [warning: MPC header version 1.0.2 differs from library version 1.0.3.]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/../../../../mingw32/include"]
        ignore line: [ignoring duplicate directory "c:/mingw/lib/gcc/../../lib/gcc/mingw32/6.3.0/include/c++"]
        ignore line: [ignoring duplicate directory "c:/mingw/lib/gcc/../../lib/gcc/mingw32/6.3.0/include/c++/mingw32"]
        ignore line: [ignoring duplicate directory "c:/mingw/lib/gcc/../../lib/gcc/mingw32/6.3.0/include/c++/backward"]
        ignore line: [ignoring duplicate directory "c:/mingw/lib/gcc/../../lib/gcc/mingw32/6.3.0/include"]
        ignore line: [ignoring duplicate directory "/mingw/lib/gcc/mingw32/6.3.0/../../../../include"]
        ignore line: [ignoring duplicate directory "c:/mingw/lib/gcc/../../include"]
        ignore line: [ignoring duplicate directory "c:/mingw/lib/gcc/../../lib/gcc/mingw32/6.3.0/include-fixed"]
        ignore line: [ignoring nonexistent directory "c:/mingw/lib/gcc/../../lib/gcc/mingw32/6.3.0/../../../../mingw32/include"]
        ignore line: [ignoring duplicate directory "/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/include/c++]
        ignore line: [ c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/include/c++/mingw32]
        ignore line: [ c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/include/c++/backward]
        ignore line: [ c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/include]
        ignore line: [ c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/../../../../include]
        ignore line: [ c:\\mingw\\bin\\../lib/gcc/mingw32/6.3.0/include-fixed]
        ignore line: [End of search list.]
        ignore line: [GNU C++14 (MinGW.org GCC-6.3.0-1) version 6.3.0 (mingw32)]
        ignore line: [	compiled by GNU C version 6.3.0  GMP version 6.1.2  MPFR version 3.1.5  MPC version 1.0.2  isl version 0.15]
        ignore line: [warning: MPC header version 1.0.2 differs from library version 1.0.3.]
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: af09a87986453bf79da3565f33c7648f]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=i586']
        ignore line: [ c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/bin/as.exe -v -o CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccijoLTm.s]
        ignore line: [GNU assembler version 2.28 (mingw32) using BFD version (GNU Binutils) 2.28]
        ignore line: [COMPILER_PATH=c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/]
        ignore line: [c:/mingw/bin/../libexec/gcc/]
        ignore line: [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/bin/]
        ignore line: [LIBRARY_PATH=c:/mingw/bin/../lib/gcc/mingw32/6.3.0/]
        ignore line: [c:/mingw/bin/../lib/gcc/]
        ignore line: [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/lib/]
        ignore line: [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=i586']
        ignore line: [[2/2] C:\\WINDOWS\\system32\\cmd.exe /C "cd . && C:\\MinGW\\bin\\g++.exe  -v -Wl -v CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_63431.exe -Wl --out-implib libcmTC_63431.dll.a -Wl --major-image-version 0 --minor-image-version 0   && cd ."]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\MinGW\\bin\\g++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/lto-wrapper.exe]
        ignore line: [Target: mingw32]
        ignore line: [Configured with: ../src/gcc-6.3.0/configure --build=x86_64-pc-linux-gnu --host=mingw32 --with-gmp=/mingw --with-mpfr=/mingw --with-mpc=/mingw --with-isl=/mingw --prefix=/mingw --disable-win32-registry --target=mingw32 --with-arch=i586 --enable-languages=c c++ objc obj-c++ fortran ada --with-pkgversion='MinGW.org GCC-6.3.0-1' --enable-static --enable-shared --enable-threads --with-dwarf2 --disable-sjlj-exceptions --enable-version-specific-runtime-libs --with-libiconv-prefix=/mingw --with-libintl-prefix=/mingw --enable-libstdcxx-debug --with-tune=generic --enable-libgomp --disable-libvtv --enable-nls]
        ignore line: [Thread model: win32]
        ignore line: [gcc version 6.3.0 (MinGW.org GCC-6.3.0-1) ]
        ignore line: [COMPILER_PATH=c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/]
        ignore line: [c:/mingw/bin/../libexec/gcc/]
        ignore line: [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/bin/]
        ignore line: [LIBRARY_PATH=c:/mingw/bin/../lib/gcc/mingw32/6.3.0/]
        ignore line: [c:/mingw/bin/../lib/gcc/]
        ignore line: [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/lib/]
        ignore line: [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_63431.exe' '-shared-libgcc' '-mtune=generic' '-march=i586']
        link line: [ c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/collect2.exe -plugin c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/liblto_plugin-0.dll -plugin-opt=c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8jfa2v.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -Bdynamic -u ___register_frame_info -u ___deregister_frame_info -o cmTC_63431.exe c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../crt2.o c:/mingw/bin/../lib/gcc/mingw32/6.3.0/crtbegin.o -Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0 -Lc:/mingw/bin/../lib/gcc -Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/lib -Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../.. -v CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_63431.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt c:/mingw/bin/../lib/gcc/mingw32/6.3.0/crtend.o]
          arg [c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/liblto_plugin-0.dll] ==> ignore
          arg [-plugin-opt=c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8jfa2v.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-u] ==> ignore
          arg [___register_frame_info] ==> ignore
          arg [-u] ==> ignore
          arg [___deregister_frame_info] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_63431.exe] ==> ignore
          arg [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../crt2.o] ==> obj [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../crt2.o]
          arg [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/crtbegin.o] ==> obj [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/crtbegin.o]
          arg [-Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0] ==> dir [c:/mingw/bin/../lib/gcc/mingw32/6.3.0]
          arg [-Lc:/mingw/bin/../lib/gcc] ==> dir [c:/mingw/bin/../lib/gcc]
          arg [-Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/lib] ==> dir [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/lib]
          arg [-Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../..] ==> dir [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_63431.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/crtend.o] ==> obj [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/crtend.o]
        ignore line: [collect2 version 6.3.0]
        ignore line: [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/bin/ld.exe -plugin c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/liblto_plugin-0.dll -plugin-opt=c:/mingw/bin/../libexec/gcc/mingw32/6.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8jfa2v.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -Bdynamic -u ___register_frame_info -u ___deregister_frame_info -o cmTC_63431.exe c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../crt2.o c:/mingw/bin/../lib/gcc/mingw32/6.3.0/crtbegin.o -Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0 -Lc:/mingw/bin/../lib/gcc -Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/lib -Lc:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../.. -v CMakeFiles/cmTC_63431.dir/CMakeCXXCompilerABI.cpp.obj --out-implib libcmTC_63431.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt c:/mingw/bin/../lib/gcc/mingw32/6.3.0/crtend.o]
        linker tool for 'CXX': c:/mingw/mingw32/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../crt2.o] ==> [C:/MinGW/lib/crt2.o]
        collapse obj [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/crtbegin.o] ==> [C:/MinGW/lib/gcc/mingw32/6.3.0/crtbegin.o]
        collapse obj [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/crtend.o] ==> [C:/MinGW/lib/gcc/mingw32/6.3.0/crtend.o]
        collapse library dir [c:/mingw/bin/../lib/gcc/mingw32/6.3.0] ==> [C:/MinGW/lib/gcc/mingw32/6.3.0]
        collapse library dir [c:/mingw/bin/../lib/gcc] ==> [C:/MinGW/lib/gcc]
        collapse library dir [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../../../mingw32/lib] ==> [C:/MinGW/mingw32/lib]
        collapse library dir [c:/mingw/bin/../lib/gcc/mingw32/6.3.0/../../..] ==> [C:/MinGW/lib]
        implicit libs: [stdc++;mingw32;gcc_s;gcc;moldname;mingwex;advapi32;shell32;user32;kernel32;mingw32;gcc_s;gcc;moldname;mingwex]
        implicit objs: [C:/MinGW/lib/crt2.o;C:/MinGW/lib/gcc/mingw32/6.3.0/crtbegin.o;C:/MinGW/lib/gcc/mingw32/6.3.0/crtend.o]
        implicit dirs: [C:/MinGW/lib/gcc/mingw32/6.3.0;C:/MinGW/lib/gcc;C:/MinGW/mingw32/lib;C:/MinGW/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "c:/mingw/mingw32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.28
...
