{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["C:/MinGW/lib/gcc/mingw32/6.3.0/include/c++", "C:/MinGW/lib/gcc/mingw32/6.3.0/include/c++/mingw32", "C:/MinGW/lib/gcc/mingw32/6.3.0/include/c++/backward", "C:/MinGW/lib/gcc/mingw32/6.3.0/include", "C:/MinGW/include", "C:/MinGW/lib/gcc/mingw32/6.3.0/include-fixed"], "linkDirectories": ["C:/MinGW/lib/gcc/mingw32/6.3.0", "C:/MinGW/lib/gcc", "C:/MinGW/mingw32/lib", "C:/MinGW/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "mingw32", "gcc_s", "gcc", "moldname", "mingwex", "advapi32", "shell32", "user32", "kernel32", "mingw32", "gcc_s", "gcc", "moldname", "mingwex"]}, "path": "C:/MinGW/bin/g++.exe", "version": "6.3.0"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "m", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}, {"compiler": {"implicit": {}, "path": "C:/MinGW/bin/windres.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}