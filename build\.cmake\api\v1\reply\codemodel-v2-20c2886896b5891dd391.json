{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "4.0.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "automata", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "automata::@6890427a1f51a3e7e1df", "jsonFile": "target-automata-Debug-bf8a56bc0307af3e29fb.json", "name": "automata", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/OneDrive/Desktop/C++/automata/build", "source": "C:/Users/<USER>/OneDrive/Desktop/C++/automata"}, "version": {"major": 2, "minor": 8}}